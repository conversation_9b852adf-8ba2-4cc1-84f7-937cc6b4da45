<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Today Page</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Geist', -apple-system, BlinkMacSystemFont, sans-serif;
            background-color: #ffffff;
        }

        img {
            display: block;
        }

        .today-page {
            width: 3.93rem;
            height: 8.52rem;
            background-color: #ffffff;
            position: relative;
        }

        .today-page-container {
            width: 3.61rem;
            height: 8.2rem;
            position: absolute;
            left: 0.16rem;
            top: 0.32rem;
        }

        .container-1 {
            width: 3.61rem;
            height: 3.62rem;
            position: relative;
        }

        .page-title-default {
            width: 3.61rem;
            height: 0.24rem;
            background-color: #ffffff;
            position: relative;
        }

        .menu-icon {
            width: 0.24rem;
            height: 0.24rem;
            background-image: url('./images/menu-icon.png');
            background-size: contain;
            background-repeat: no-repeat;
            background-position: center;
            position: absolute;
            left: 0;
            top: 0;
        }

        .index-text {
            position: absolute;
            left: 0.24rem;
            top: 0.015rem;
            width: 3.13rem;
            height: 0.21rem;
            font-family: Geist;
            font-weight: 500;
            font-size: 0.16rem;
            line-height: 1.3;
            text-align: center;
            color: #000000;
        }

        .back-icon {
            width: 0.24rem;
            height: 0.24rem;
            background-image: url('./images/back-icon.png');
            background-size: contain;
            background-repeat: no-repeat;
            background-position: center;
            position: absolute;
            right: 0;
            top: 0;
        }

        .container-2 {
            width: 3.61rem;
            height: 6.72rem;
            position: absolute;
            top: 0.44rem;
        }

        .todo-list-container {
            width: 3.61rem;
            height: 3.26rem;
            background-color: #ffffff;
            border-radius: 0.08rem;
            position: relative;
        }

        .title-section {
            width: 3.61rem;
            height: 0.24rem;
            position: relative;
        }

        .todo-list-text {
            position: absolute;
            left: 0;
            top: 0.03rem;
            width: 3.37rem;
            height: 0.18rem;
            font-family: Geist;
            font-weight: 500;
            font-size: 0.14rem;
            line-height: 1.3;
            color: #000000;
        }

        .plus-icon {
            width: 0.24rem;
            height: 0.24rem;
            background-image: url('./images/plus-icon.png');
            background-size: contain;
            background-repeat: no-repeat;
            background-position: center;
            background-color: #f1f1f1;
            border-radius: 100%;
            position: absolute;
            right: 0;
            top: 0;
        }

        .mobile-app-container {
            width: 3.61rem;
            height: 2.86rem;
            position: absolute;
            top: 0.4rem;
        }

        .card-1 {
            width: 3.61rem;
            height: 1.35rem;
            background-color: #ffffff;
            border: 1px solid #ececec;
            border-radius: 0.08rem;
            position: relative;
        }

        .card-1-container {
            width: 3.45rem;
            height: 0.7rem;
            position: absolute;
            left: 0.08rem;
            top: 0.12rem;
        }

        .card-1-title-section {
            width: 3.45rem;
            height: 0.33rem;
            position: relative;
        }

        .card-1-title-desc {
            width: 3.27rem;
            height: 0.33rem;
            position: relative;
        }

        .mobile-app-design-text {
            width: 3.27rem;
            height: 0.16rem;
            font-family: Geist;
            font-weight: 500;
            font-size: 0.12rem;
            line-height: 1.3;
            color: #000000;
        }

        .wireframing-text {
            width: 3.27rem;
            height: 0.13rem;
            font-family: Geist;
            font-weight: 400;
            font-size: 0.1rem;
            line-height: 1.3;
            color: #717171;
            position: absolute;
            top: 0.2rem;
        }

        .dots-icon {
            width: 0.18rem;
            height: 0.18rem;
            background-image: url('./images/dots-icon.png');
            background-size: contain;
            background-repeat: no-repeat;
            background-position: center;
            border-radius: 100%;
            position: absolute;
            right: 0;
            top: 0;
        }

        .progress-section {
            width: 3.45rem;
            height: 0.29rem;
            position: absolute;
            top: 0.41rem;
        }

        .progress-30-text {
            width: 3.45rem;
            height: 0.13rem;
            font-family: Geist;
            font-weight: 400;
            font-size: 0.1rem;
            line-height: 1.3;
            text-align: right;
            color: #000000;
        }

        .progress-bar-30 {
            width: 3.45rem;
            height: 0.08rem;
            background-color: #c6c6c6;
            border-radius: 0.08rem;
            position: absolute;
            top: 0.21rem;
        }

        .progress-bar-30-fill {
            width: 0.84rem;
            height: 0.08rem;
            background-color: #000000;
            border-radius: 0.08rem;
        }

        .card-1-bottom {
            width: 3.45rem;
            height: 0.25rem;
            position: absolute;
            top: 1.02rem;
        }

        .date-tag {
            width: 0.92rem;
            height: 0.25rem;
            background-color: #f1f1f1;
            border-radius: 0.12rem;
            position: relative;
        }

        .date-text {
            position: absolute;
            left: 0.1rem;
            top: 0.06rem;
            width: 0.72rem;
            height: 0.13rem;
            font-family: Geist;
            font-weight: 400;
            font-size: 0.1rem;
            line-height: 1.3;
            color: #717171;
        }

        .icons-section {
            width: 0.6rem;
            height: 0.18rem;
            position: absolute;
            right: 0;
            top: 0.035rem;
        }

        .comment-section {
            width: 0.29rem;
            height: 0.18rem;
            position: relative;
        }

        .comment-icon {
            width: 0.18rem;
            height: 0.18rem;
            background-image: url('./images/comment-icon.png');
            background-size: contain;
            background-repeat: no-repeat;
            background-position: center;
        }

        .comment-count {
            position: absolute;
            left: 0.2rem;
            top: 0.025rem;
            width: 0.07rem;
            height: 0.13rem;
            font-family: Geist;
            font-weight: 500;
            font-size: 0.1rem;
            line-height: 1.3;
            color: #717171;
        }

        .link-section {
            width: 0.27rem;
            height: 0.18rem;
            position: absolute;
            right: 0;
        }

        .link-icon {
            width: 0.18rem;
            height: 0.18rem;
            background-image: url('./images/comment-icon.png');
            background-size: contain;
            background-repeat: no-repeat;
            background-position: center;
        }

        .link-count {
            position: absolute;
            left: 0.2rem;
            top: 0.025rem;
            width: 0.07rem;
            height: 0.13rem;
            font-family: Geist;
            font-weight: 500;
            font-size: 0.1rem;
            line-height: 1.3;
            color: #717171;
        }

        .card-2 {
            width: 3.61rem;
            height: 1.35rem;
            background-color: #ffffff;
            border: 1px solid #ececec;
            border-radius: 0.08rem;
            position: absolute;
            top: 1.51rem;
        }

        .card-2-container {
            width: 3.45rem;
            height: 0.7rem;
            position: absolute;
            left: 0.08rem;
            top: 0.12rem;
        }

        .card-2-title-section {
            width: 3.45rem;
            height: 0.33rem;
            position: relative;
        }

        .card-2-title-desc {
            width: 3.27rem;
            height: 0.33rem;
            position: relative;
        }

        .website-design-text {
            width: 3.27rem;
            height: 0.16rem;
            font-family: Geist;
            font-weight: 500;
            font-size: 0.12rem;
            line-height: 1.3;
            color: #000000;
        }

        .wireframing-text-2 {
            width: 1.24rem;
            height: 0.13rem;
            font-family: Geist;
            font-weight: 400;
            font-size: 0.1rem;
            line-height: 1.3;
            color: #717171;
            position: absolute;
            top: 0.2rem;
        }

        .dots-icon-2 {
            width: 0.18rem;
            height: 0.18rem;
            background-image: url('./images/dots-icon4.png');
            background-size: contain;
            background-repeat: no-repeat;
            background-position: center;
            border-radius: 100%;
            position: absolute;
            right: 0;
            top: 0;
        }

        .progress-section-2 {
            width: 3.45rem;
            height: 0.29rem;
            position: absolute;
            top: 0.41rem;
        }

        .progress-70-text {
            width: 3.45rem;
            height: 0.13rem;
            font-family: Geist;
            font-weight: 400;
            font-size: 0.1rem;
            line-height: 1.3;
            text-align: right;
            color: #000000;
        }

        .progress-bar-70 {
            width: 3.45rem;
            height: 0.08rem;
            background-color: #d3caff;
            border-radius: 0.08rem;
            position: absolute;
            top: 0.21rem;
        }

        .progress-bar-70-fill {
            width: 2.22rem;
            height: 0.08rem;
            background-color: #6446ff;
            border-radius: 0.08rem;
        }

        .card-2-bottom {
            width: 3.45rem;
            height: 0.25rem;
            position: absolute;
            top: 1.02rem;
        }

        .date-tag-2 {
            width: 0.92rem;
            height: 0.25rem;
            background-color: #f1f1f1;
            border-radius: 0.12rem;
            position: relative;
        }

        .date-text-2 {
            position: absolute;
            left: 0.1rem;
            top: 0.06rem;
            width: 0.72rem;
            height: 0.13rem;
            font-family: Geist;
            font-weight: 400;
            font-size: 0.1rem;
            line-height: 1.3;
            color: #717171;
        }

        .icons-section-2 {
            width: 0.6rem;
            height: 0.18rem;
            position: absolute;
            right: 0;
            top: 0.035rem;
        }

        .comment-section-2 {
            width: 0.29rem;
            height: 0.18rem;
            position: relative;
        }

        .comment-icon-2 {
            width: 0.18rem;
            height: 0.18rem;
            background-image: url('./images/comment-icon4.png');
            background-size: contain;
            background-repeat: no-repeat;
            background-position: center;
        }

        .comment-count-2 {
            position: absolute;
            left: 0.2rem;
            top: 0.025rem;
            width: 0.07rem;
            height: 0.13rem;
            font-family: Geist;
            font-weight: 500;
            font-size: 0.1rem;
            line-height: 1.3;
            color: #717171;
        }

        .link-section-2 {
            width: 0.27rem;
            height: 0.18rem;
            position: absolute;
            right: 0;
        }

        .link-icon-2 {
            width: 0.18rem;
            height: 0.18rem;
            background-image: url('./images/link-icon4.png');
            background-size: contain;
            background-repeat: no-repeat;
            background-position: center;
        }

        .link-count-2 {
            position: absolute;
            left: 0.2rem;
            top: 0.025rem;
            width: 0.07rem;
            height: 0.13rem;
            font-family: Geist;
            font-weight: 500;
            font-size: 0.1rem;
            line-height: 1.3;
            color: #717171;
        }

        .app-bar {
            width: 3.61rem;
            height: 0.45rem;
            background-color: #ffffff;
            position: absolute;
            bottom: 0;
        }

        .today-section {
            width: 0.33rem;
            height: 0.37rem;
            position: absolute;
            left: 0.12rem;
            top: 0.04rem;
        }

        .today-icon {
            width: 0.24rem;
            height: 0.24rem;
            background-image: url('./images/today-bottom.png');
            background-size: contain;
            background-repeat: no-repeat;
            background-position: center;
            position: absolute;
            left: 0.045rem;
            top: 0;
        }

        .today-text {
            position: absolute;
            left: 0;
            top: 0.24rem;
            width: 0.33rem;
            height: 0.13rem;
            font-family: Geist;
            font-weight: 400;
            font-size: 0.1rem;
            line-height: 1.3;
            text-align: center;
            color: #6446ff;
        }

        .calendar-section {
            width: 0.43rem;
            height: 0.37rem;
            position: absolute;
            left: 0.8575rem;
            top: 0.04rem;
        }

        .calendar-icon {
            width: 0.24rem;
            height: 0.24rem;
            background-image: url('./images/calendar-icon.png');
            background-size: contain;
            background-repeat: no-repeat;
            background-position: center;
            position: absolute;
            left: 0.095rem;
            top: 0;
        }

        .calendar-text {
            position: absolute;
            left: 0;
            top: 0.24rem;
            width: 0.43rem;
            height: 0.13rem;
            font-family: Geist;
            font-weight: 400;
            font-size: 0.1rem;
            line-height: 1.3;
            color: #000000;
        }

        .plus-center {
            width: 0.32rem;
            height: 0.32rem;
            background-image: url('./images/plus-bottom.png');
            background-size: contain;
            background-repeat: no-repeat;
            background-position: center;
            background-color: #6446ff;
            border-radius: 100%;
            position: absolute;
            left: 1.695rem;
            top: 0.065rem;
        }

        .search-section {
            width: 0.33rem;
            height: 0.37rem;
            position: absolute;
            left: 2.4225rem;
            top: 0.04rem;
        }

        .search-icon {
            width: 0.24rem;
            height: 0.24rem;
            background-image: url('./images/search-icon.png');
            background-size: contain;
            background-repeat: no-repeat;
            background-position: center;
            position: absolute;
            left: 0.045rem;
            top: 0;
        }

        .search-text {
            position: absolute;
            left: 0;
            top: 0.24rem;
            width: 0.33rem;
            height: 0.13rem;
            font-family: Geist;
            font-weight: 400;
            font-size: 0.1rem;
            line-height: 1.3;
            color: #000000;
        }

        .profile-section {
            width: 0.33rem;
            height: 0.37rem;
            position: absolute;
            right: 0.12rem;
            top: 0.04rem;
        }

        .profile-icon {
            width: 0.24rem;
            height: 0.24rem;
            background-image: url('./images/profile-icon.png');
            background-size: contain;
            background-repeat: no-repeat;
            background-position: center;
            position: absolute;
            left: 0.045rem;
            top: 0;
        }

        .profile-text {
            position: absolute;
            left: 0;
            top: 0.24rem;
            width: 0.33rem;
            height: 0.13rem;
            font-family: Geist;
            font-weight: 400;
            font-size: 0.1rem;
            line-height: 1.3;
            text-align: center;
            color: #000000;
        }
    </style>
</head>
<body>
    <div class="today-page">
        <div class="today-page-container">
            <div class="container-1">
                <div class="page-title-default">
                    <div class="menu-icon"></div>
                    <div class="index-text">Index</div>
                    <div class="back-icon"></div>
                </div>
                <div class="container-2">
                    <div class="todo-list-container">
                        <div class="title-section">
                            <div class="todo-list-text">To do List</div>
                            <div class="plus-icon"></div>
                        </div>
                        <div class="mobile-app-container">
                            <div class="card-1">
                                <div class="card-1-container">
                                    <div class="card-1-title-section">
                                        <div class="card-1-title-desc">
                                            <div class="mobile-app-design-text">Mobile App Design</div>
                                            <div class="wireframing-text">Wireframing, Colors, Fonts</div>
                                        </div>
                                        <div class="dots-icon"></div>
                                    </div>
                                    <div class="progress-section">
                                        <div class="progress-30-text">Progress 30%</div>
                                        <div class="progress-bar-30">
                                            <div class="progress-bar-30-fill"></div>
                                        </div>
                                    </div>
                                </div>
                                <div class="card-1-bottom">
                                    <div class="date-tag">
                                        <div class="date-text">22 March 2025</div>
                                    </div>
                                    <div class="icons-section">
                                        <div class="comment-section">
                                            <div class="comment-icon"></div>
                                            <div class="comment-count">3</div>
                                        </div>
                                        <div class="link-section">
                                            <div class="link-icon"></div>
                                            <div class="link-count">5</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="card-2">
                                <div class="card-2-container">
                                    <div class="card-2-title-section">
                                        <div class="card-2-title-desc">
                                            <div class="website-design-text">Website Design</div>
                                            <div class="wireframing-text-2">Wireframing, Colors, Fonts</div>
                                        </div>
                                        <div class="dots-icon-2"></div>
                                    </div>
                                    <div class="progress-section-2">
                                        <div class="progress-70-text">Progress 70%</div>
                                        <div class="progress-bar-70">
                                            <div class="progress-bar-70-fill"></div>
                                        </div>
                                    </div>
                                </div>
                                <div class="card-2-bottom">
                                    <div class="date-tag-2">
                                        <div class="date-text-2">22 March 2025</div>
                                    </div>
                                    <div class="icons-section-2">
                                        <div class="comment-section-2">
                                            <div class="comment-icon-2"></div>
                                            <div class="comment-count-2">3</div>
                                        </div>
                                        <div class="link-section-2">
                                            <div class="link-icon-2"></div>
                                            <div class="link-count-2">5</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="app-bar">
                <div class="today-section">
                    <div class="today-icon"></div>
                    <div class="today-text">Today</div>
                </div>
                <div class="calendar-section">
                    <div class="calendar-icon"></div>
                    <div class="calendar-text">Calendar</div>
                </div>
                <div class="plus-center"></div>
                <div class="search-section">
                    <div class="search-icon"></div>
                    <div class="search-text">Search</div>
                </div>
                <div class="profile-section">
                    <div class="profile-icon"></div>
                    <div class="profile-text">Profile</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        (function () {
            const designWidth = 393; // 设计稿宽度
            const baseRem = 100;      // 设定 1rem = 100px，方便换算

            function setRootFontSize() {
                const html = document.documentElement;
                const clientWidth = html.clientWidth;

                // 让页面宽度和设计稿成等比缩放
                html.style.fontSize = (clientWidth / designWidth) * baseRem + 'px';
            }

            setRootFontSize();
            window.addEventListener('resize', setRootFontSize);
        })();
    </script>
</body>
</html>
